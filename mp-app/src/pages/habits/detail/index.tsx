import { Button, View } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import dayjs from "dayjs";
import { useEffect, useState } from "react"
import { CheckinCalendar } from "src/components";
import { checkinHabit, getHabitDetail, getHabitsStatistics } from "src/services/habits"
import { Image } from '@nutui/nutui-react-taro';
import './index.scss'
import HabitIcon from '../../../assets/habits/sleep.png';
import Taro from "@tarojs/taro";
import { Alarm } from '@nutui/icons-react-taro'

const HabitDetail = () => {
  const router = useRouter();
  const { id } = router.params;

  const [habit, setHabit] = useState<HabitItem>()
  const [habitStatistics, setHabitStatistics] = useState<HabitStatistics>()

  useEffect(() => {
    getHabitDetail(id).then(setHabit)
    getHabitsStatistics(id).then(setHabitStatistics)
  }, [])


  const handleDayClick = (check_in_date: string) => {
    console.log('打卡日期', check_in_date)
    // checkinHabit({
    //   habit_id: id,
    //   check_in_date: dayjs().format('YYYY-MM-DD'),
    //   notes: ''
    // })
  };

  return <View className='habit-detail-page'>
    <View className="card">
      <View className="habit-info">
        {habit?.icon && <Image
            src={require(`../../../assets/habits/${habit?.icon}.png`)}
            mode="aspectFit"
            width={50}
            height={50}
          />}
        <View className="habit-info-content ml-2">
          <View className="text-lg">{habit?.name}</View>
          <View className="text-gray-600 text-sm">{habit?.description}</View>
        </View>
      </View>
      {habit?.preferred_time  && <div className="mt-2 text-gray-600 text-sm flex items-center">
        <Alarm  size={
          18
        }/> 
        <span className="ml-2">提醒时间：{habit?.preferred_time}</span>
      </div>}
    </View>
    <View className="card">
      <CheckinCalendar
        data={habitStatistics?.current_month_data ?? []}
        title=""
        onDayClick={handleDayClick}
        showStats={false}
      />
    </View>

  
   

    <div className="flex justify-between gap-2">
      <Button className="flex-1" type="primary"
       onClick={handleDayClick}>立即打卡</Button>
      <Button 
        className="flex-1" 
        onClick={() => {
          Taro.navigateTo({
            url: `/pages/habits/create/index?id=${id}`,
          })
        }}
      >编辑习惯</Button>
    </div>
  </View>
}

export default HabitDetail